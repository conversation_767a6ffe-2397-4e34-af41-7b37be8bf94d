/**
 * HTML解析和上标提取工具函数
 * 用于解析HTML内容并提取上标元素及其相关信息
 */

import { getAllAnnotationNumbers } from './annotationUtils'

/**
 * 上标数据接口定义
 */
export interface SuperscriptData {
  /** 上标的数值内容 */
  value: string
  /** 上标的数字编号（如果是数字格式） */
  number?: number
  /** 上标在HTML中的位置索引 */
  position: number
  /** 上标的原始HTML内容 */
  rawHtml: string
  /** 上标周围的上下文文本 */
  context?: string
}

/**
 * 页面内容解析结果接口
 */
export interface PageContentData {
  /** 完整的HTML内容 */
  fullHtml: string
  /** 提取的所有上标数据 */
  superscripts: SuperscriptData[]
  /** 上标数值数组（简化格式） */
  superscriptValues: string[]
  /** 上标数字编号数组（仅数字格式的上标） */
  superscriptNumbers: number[]
  /** 解析时间戳 */
  timestamp: number
}

/**
 * 从HTML内容中提取所有上标元素
 * @param htmlContent HTML内容字符串
 * @param includeContext 是否包含上下文信息，默认为true
 * @param contextLength 上下文长度，默认为50个字符
 * @returns 上标数据数组
 */
export const extractSuperscriptsFromHTML = (
  htmlContent: string,
  includeContext: boolean = true,
  contextLength: number = 50
): SuperscriptData[] => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return []
  }

  try {
    const superscripts: SuperscriptData[] = []

    // 使用更全面的正则表达式来匹配各种上标格式
    const comprehensiveSupRegex = /<sup[^>]*>(.*?)<\/sup>/gi
    let match

    while ((match = comprehensiveSupRegex.exec(htmlContent)) !== null) {
      const fullMatch = match[0] // 完整的上标HTML
      const content = match[1] // 上标内容
      const position = match.index // 在HTML中的位置

      // 提取数字编号（如果存在）
      let number: number | undefined
      const numberMatch = content.match(/\[?(\d+)\]?/)
      if (numberMatch) {
        number = parseInt(numberMatch[1], 10)
      }

      // 提取上下文信息
      let context: string | undefined
      if (includeContext) {
        const startPos = Math.max(0, position - contextLength)
        const endPos = Math.min(htmlContent.length, position + fullMatch.length + contextLength)
        context = htmlContent
          .substring(startPos, endPos)
          .replace(/<[^>]*>/g, '') // 移除HTML标签
          .trim()
      }

      superscripts.push({
        value: content.trim(),
        number,
        position,
        rawHtml: fullMatch,
        context
      })
    }

    // 按位置排序
    return superscripts.sort((a, b) => a.position - b.position)
  } catch (error) {
    console.error('提取上标时发生错误:', error)
    return []
  }
}

/**
 * 解析选中内容，提取HTML和上标信息
 * @param editor TipTap编辑器实例
 * @param from 选择开始位置
 * @param to 选择结束位置
 * @param includeContext 是否包含上下文信息
 * @returns 选中内容解析结果
 */
export const parseSelectedContent = (
  editor: any,
  from: number,
  to: number,
  includeContext: boolean = true
): PageContentData => {
  if (!editor || !editor.state || from === to) {
    return {
      fullHtml: '',
      superscripts: [],
      superscriptValues: [],
      superscriptNumbers: [],
      timestamp: Date.now()
    }
  }

  try {
    // 获取选中范围的HTML内容
    const selectedHtml = getSelectedHTMLContent(editor, from, to)
    console.log('selectedHtml ==>', selectedHtml)
    if (!selectedHtml || selectedHtml.trim() === '') {
      return {
        fullHtml: '',
        superscripts: [],
        superscriptValues: [],
        superscriptNumbers: [],
        timestamp: Date.now()
      }
    }

    // 提取上标数据
    const superscripts = extractSuperscriptsFromHTML(selectedHtml, includeContext)
    console.log('superscripts ==>', superscripts)
    // 生成简化的数据格式
    const superscriptValues = superscripts.map((sup) => sup.value)
    const superscriptNumbers = superscripts
      .filter((sup) => sup.number !== undefined)
      .map((sup) => sup.number!)
      .filter((num, index, arr) => arr.indexOf(num) === index) // 去重
      .sort((a, b) => a - b)

    return {
      fullHtml: selectedHtml,
      superscripts,
      superscriptValues,
      superscriptNumbers,
      timestamp: Date.now()
    }
  } catch (error) {
    console.error('解析选中内容时发生错误:', error)
    return {
      fullHtml: '',
      superscripts: [],
      superscriptValues: [],
      superscriptNumbers: [],
      timestamp: Date.now()
    }
  }
}

/**
 * 获取选中内容的HTML格式
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} from - 选择开始位置
 * @param {number} to - 选择结束位置
 * @returns {string} 选中内容的HTML字符串
 */
const getSelectedHTMLContent = (editor: any, from: number, to: number): string => {
  console.log('getSelectedHTMLContent 调用参数:', { from, to, hasEditor: !!editor })

  if (!editor || !editor.state || from === to) {
    console.log('getSelectedHTMLContent 早期返回: 无效参数')
    return ''
  }

  try {
    // 首先尝试最简单有效的方法（基于 HomeView.vue 的工作实现）
    const { state } = editor
    const slice = state.doc.slice(from, to)
    console.log('slice 创建成功:', { hasContent: !!slice.content, size: slice.size })

    // 方法0: 直接使用 HomeView.vue 中验证的逻辑
    console.log('尝试方法0: HomeView.vue 验证的逻辑')
    if (state.schema && state.schema.serializer) {
      try {
        const fragment = state.schema.serializer.serializeFragment(slice.content)
        const tempDiv = document.createElement('div')
        tempDiv.appendChild(fragment)
        const htmlResult = tempDiv.innerHTML
        console.log('方法0 HTML 结果:', htmlResult)
        if (htmlResult && htmlResult.trim()) {
          return htmlResult
        }
      } catch (serializerError) {
        console.log('方法0 serializer 错误:', serializerError)
      }
    }

    // 如果方法0失败，尝试JSON重建（HomeView.vue风格）
    const sliceJSON = slice.content.toJSON()
    if (sliceJSON && sliceJSON.content) {
      console.log('方法0b: JSON 重建（HomeView.vue 风格）')
      let reconstructedHTML = ''

      for (const node of sliceJSON.content) {
        if (node.type === 'paragraph') {
          let paragraphHTML = '<p>'
          if (node.content) {
            for (const inline of node.content) {
              if (inline.type === 'text') {
                if (inline.marks) {
                  for (const mark of inline.marks) {
                    if (mark.type === 'superscript') {
                      paragraphHTML += `<sup>${inline.text}</sup>`
                    } else {
                      paragraphHTML += inline.text
                    }
                  }
                } else {
                  paragraphHTML += inline.text
                }
              }
            }
          }
          paragraphHTML += '</p>'
          reconstructedHTML += paragraphHTML
        }
      }

      console.log('方法0b 重建的HTML:', reconstructedHTML)
      if (reconstructedHTML && reconstructedHTML.trim()) {
        return reconstructedHTML
      }
    }

    // 尝试使用schema的serializer（改进版）
    if (state.schema) {
      console.log('尝试使用 schema serializer')
      try {
        // 检查多种可能的serializer访问方式
        let serializer = state.schema.serializer
        if (!serializer && state.schema.cached && state.schema.cached.serializer) {
          serializer = state.schema.cached.serializer
        }

        if (serializer && typeof serializer.serializeFragment === 'function') {
          const fragment = serializer.serializeFragment(slice.content)
          console.log('serializer fragment 创建成功:', !!fragment)
          const tempDiv = document.createElement('div')
          tempDiv.appendChild(fragment)
          const htmlResult = tempDiv.innerHTML
          console.log('方法1 HTML 结果:', htmlResult)
          if (htmlResult && htmlResult.trim()) {
            return htmlResult
          }
        } else {
          console.log('schema serializer 方法不可用')
        }
      } catch (serializerError) {
        console.log('schema serializer 错误:', serializerError)
      }
    } else {
      console.log('schema 不可用')
    }

    // 方法2: 尝试使用 DOMSerializer（ProseMirror 的标准方法）
    console.log('尝试方法2: DOMSerializer')
    try {
      // 检查是否有 DOMSerializer 可用
      const DOMSerializer =
        (window as any).DOMSerializer ||
        (globalThis as any).DOMSerializer ||
        state.schema.DOMSerializer

      if (DOMSerializer && typeof DOMSerializer.fromSchema === 'function') {
        const serializer = DOMSerializer.fromSchema(state.schema)
        const fragment = serializer.serializeFragment(slice.content)
        const tempDiv = document.createElement('div')
        tempDiv.appendChild(fragment)
        const htmlResult = tempDiv.innerHTML
        console.log('方法2 DOMSerializer 结果:', htmlResult)
        if (htmlResult && htmlResult.trim()) {
          return htmlResult
        }
      } else {
        console.log('DOMSerializer 不可用')
      }
    } catch (error) {
      console.log('方法2 DOMSerializer 错误:', error)
    }

    // 方法3: 尝试使用 TipTap 的内置 HTML 序列化方法
    console.log('尝试方法3: TipTap 内置序列化方法')
    try {
      // 尝试使用 TipTap 的 generateHTML 方法（如果可用）
      if (editor.schema && slice.content) {
        // 检查是否有 generateHTML 方法
        const TipTap = (window as any).TipTap || (globalThis as any).TipTap
        if (TipTap && TipTap.generateHTML) {
          const htmlResult = TipTap.generateHTML(slice.content.toJSON(), editor.schema)
          console.log('方法3 TipTap generateHTML 结果:', htmlResult)
          if (htmlResult && htmlResult.trim()) {
            return htmlResult
          }
        }
      }
    } catch (error) {
      console.log('方法3 错误:', error)
    }

    // 方法4: JSON 重建方法
    console.log('尝试方法4: JSON 重建')
    const sliceJSON = slice.content.toJSON()
    console.log('sliceJSON:', sliceJSON)

    if (sliceJSON && sliceJSON.content) {
      console.log('JSON 内容存在，开始重建HTML')
      // 尝试从JSON结构中重建HTML
      let reconstructedHTML = ''

      for (const node of sliceJSON.content) {
        console.log('处理节点类型:', node.type)

        if (node.type === 'paragraph') {
          let paragraphHTML = '<p>'
          if (node.content) {
            for (const inline of node.content) {
              if (inline.type === 'text') {
                if (inline.marks) {
                  // 处理带标记的文本（参考 HomeView.vue 的正确实现）
                  for (const mark of inline.marks) {
                    if (mark.type === 'superscript') {
                      paragraphHTML += `<sup>${inline.text}</sup>`
                    } else {
                      paragraphHTML += inline.text
                    }
                  }
                } else {
                  paragraphHTML += inline.text
                }
              }
            }
          }
          paragraphHTML += '</p>'
          reconstructedHTML += paragraphHTML
        } else if (node.type === 'heading') {
          // 处理标题节点
          const level = node.attrs?.level || 1
          let headingHTML = `<h${level}>`
          if (node.content) {
            for (const inline of node.content) {
              if (inline.type === 'text') {
                headingHTML += inline.text || ''
              }
            }
          }
          headingHTML += `</h${level}>`
          reconstructedHTML += headingHTML
        } else if (node.type === 'text') {
          // 直接文本节点
          if (node.marks && node.marks.length > 0) {
            let textWithMarks = node.text
            for (const mark of node.marks) {
              if (mark.type === 'superscript') {
                textWithMarks = `<sup>${textWithMarks}</sup>`
              }
            }
            reconstructedHTML += textWithMarks
          } else {
            reconstructedHTML += node.text || ''
          }
        }
      }

      console.log('重建的HTML:', reconstructedHTML)
      if (reconstructedHTML && reconstructedHTML.trim()) {
        return reconstructedHTML
      }
    }

    // 方法5: 回退到文本匹配方法（改进版）
    console.log('尝试方法5: 文本匹配回退方法')
    const fullHTML = editor.getHTML()
    const selectedText = state.doc.textBetween(from, to)
    console.log('选中文本:', selectedText)
    console.log('完整HTML长度:', fullHTML.length)

    if (selectedText && selectedText.trim()) {
      // 首先尝试精确的HTML片段提取
      console.log('尝试精确HTML片段提取')

      // 创建一个临时DOM来解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = fullHTML

      // 使用 TreeWalker 来遍历文本节点
      const walker = document.createTreeWalker(tempDiv, NodeFilter.SHOW_TEXT)

      let currentPos = 0
      let startNode = null
      let endNode = null
      let startOffset = 0
      let endOffset = 0

      // 查找对应位置的文本节点
      while (walker.nextNode()) {
        const textNode = walker.currentNode as Text
        const textLength = textNode.textContent?.length || 0

        if (currentPos <= from && currentPos + textLength > from) {
          startNode = textNode
          startOffset = from - currentPos
        }

        if (currentPos <= to && currentPos + textLength >= to) {
          endNode = textNode
          endOffset = to - currentPos
          break
        }

        currentPos += textLength
      }

      // 如果找到了对应的节点，尝试提取HTML
      if (startNode && endNode) {
        try {
          const range = document.createRange()
          range.setStart(startNode, startOffset)
          range.setEnd(endNode, endOffset)

          const contents = range.cloneContents()
          const extractDiv = document.createElement('div')
          extractDiv.appendChild(contents)
          const extractedHTML = extractDiv.innerHTML

          console.log('精确提取的HTML:', extractedHTML)
          if (extractedHTML && extractedHTML.trim()) {
            return extractedHTML
          }
        } catch (rangeError) {
          console.log('Range 提取错误:', rangeError)
        }
      }

      // 如果精确提取失败，回退到按行查找
      console.log('精确提取失败，尝试按行查找')
      const htmlLines = fullHTML.split('\n')
      let foundHTML = ''

      // 查找包含选中文本的行
      for (let i = 0; i < htmlLines.length; i++) {
        const line = htmlLines[i]
        if (line.includes(selectedText)) {
          // 找到包含文本的行，提取相关的HTML
          foundHTML = line
          console.log('找到包含文本的行:', foundHTML)
          break
        }
      }

      if (foundHTML && foundHTML.trim()) {
        return foundHTML
      }

      // 如果按行查找失败，尝试直接在HTML中查找
      console.log('按行查找失败，尝试直接文本匹配')
      const textIndex = fullHTML.indexOf(selectedText)
      if (textIndex !== -1) {
        console.log('在HTML中找到文本，位置:', textIndex)
        // 向前和向后扩展，寻找完整的HTML标签
        let start = textIndex
        let end = textIndex + selectedText.length

        // 向前查找，直到找到标签开始或到达合理边界
        while (start > 0 && start > textIndex - 200) {
          if (fullHTML[start] === '<') {
            break
          }
          start--
        }

        // 向后查找，直到找到标签结束或到达合理边界
        while (end < fullHTML.length && end < textIndex + selectedText.length + 200) {
          if (fullHTML[end] === '>' && end > textIndex + selectedText.length) {
            end++
            break
          }
          end++
        }

        const extractedHTML = fullHTML.substring(start, end)
        console.log('提取的HTML片段:', extractedHTML)
        return extractedHTML
      } else {
        console.log('在HTML中未找到选中文本')
      }
    }

    console.log('所有方法都失败，返回空字符串')
    return ''
  } catch (error) {
    console.error('getSelectedHTMLContent 发生错误:', error)
    console.error('错误详情:', {
      errorMessage: error instanceof Error ? error.message : String(error),
      from,
      to,
      hasEditor: !!editor,
      hasState: !!(editor && editor.state)
    })
    return ''
  }
}

/**
 * 解析页面内容，提取HTML和上标信息
 * @param editor TipTap编辑器实例
 * @param includeContext 是否包含上下文信息
 * @returns 页面内容解析结果
 */
export const parsePageContent = (editor: any, includeContext: boolean = true): PageContentData => {
  if (!editor || !editor.getHTML) {
    return {
      fullHtml: '',
      superscripts: [],
      superscriptValues: [],
      superscriptNumbers: [],
      timestamp: Date.now()
    }
  }

  try {
    // 获取完整的HTML内容
    const fullHtml = editor.getHTML()

    // 提取上标数据
    const superscripts = extractSuperscriptsFromHTML(fullHtml, includeContext)

    // 生成简化的数据格式
    const superscriptValues = superscripts.map((sup) => sup.value)
    const superscriptNumbers = superscripts
      .filter((sup) => sup.number !== undefined)
      .map((sup) => sup.number!)
      .filter((num, index, arr) => arr.indexOf(num) === index) // 去重
      .sort((a, b) => a - b)

    return {
      fullHtml,
      superscripts,
      superscriptValues,
      superscriptNumbers,
      timestamp: Date.now()
    }
  } catch (error) {
    console.error('解析页面内容时发生错误:', error)
    return {
      fullHtml: '',
      superscripts: [],
      superscriptValues: [],
      superscriptNumbers: [],
      timestamp: Date.now()
    }
  }
}

/**
 * 格式化上标数据为不同的输出格式
 * @param superscripts 上标数据数组
 * @param format 输出格式：'array' | 'object' | 'detailed'
 * @returns 格式化后的数据
 */
export const formatSuperscriptData = (
  superscripts: SuperscriptData[],
  format: 'array' | 'object' | 'detailed' = 'array'
): any => {
  if (!superscripts || superscripts.length === 0) {
    return format === 'array' ? [] : {}
  }

  switch (format) {
    case 'array':
      // 返回简单的值数组
      return superscripts.map((sup) => sup.value)

    case 'object':
      // 返回以位置为键的对象
      return superscripts.reduce(
        (acc, sup, index) => {
          acc[index] = {
            value: sup.value,
            number: sup.number,
            position: sup.position
          }
          return acc
        },
        {} as Record<number, any>
      )

    case 'detailed':
      // 返回完整的详细信息
      return {
        total: superscripts.length,
        values: superscripts.map((sup) => sup.value),
        numbers: superscripts.filter((sup) => sup.number).map((sup) => sup.number),
        positions: superscripts.map((sup) => sup.position),
        details: superscripts
      }

    default:
      return superscripts
  }
}

/**
 * 验证上标数据的完整性
 * @param superscripts 上标数据数组
 * @returns 验证结果
 */
export const validateSuperscriptData = (
  superscripts: SuperscriptData[]
): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} => {
  const errors: string[] = []
  const warnings: string[] = []

  if (!Array.isArray(superscripts)) {
    errors.push('上标数据必须是数组格式')
    return { isValid: false, errors, warnings }
  }

  // 检查重复的数字编号
  const numbers = superscripts.filter((sup) => sup.number !== undefined).map((sup) => sup.number!)

  const duplicateNumbers = numbers.filter((num, index) => numbers.indexOf(num) !== index)
  if (duplicateNumbers.length > 0) {
    warnings.push(`发现重复的上标编号: ${[...new Set(duplicateNumbers)].join(', ')}`)
  }

  // 检查空值
  const emptyValues = superscripts.filter((sup) => !sup.value || sup.value.trim() === '')
  if (emptyValues.length > 0) {
    warnings.push(`发现${emptyValues.length}个空的上标值`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 与现有annotationUtils集成的便捷函数
 * @param editor TipTap编辑器实例
 * @returns 结合两种方法的上标数据
 */
export const getComprehensiveSuperscriptData = (editor: any) => {
  try {
    // 使用现有的annotationUtils函数
    const existingNumbers = getAllAnnotationNumbers(editor)

    // 使用新的HTML解析方法
    const pageContent = parsePageContent(editor)

    return {
      // 现有方法的结果
      existingAnnotationNumbers: existingNumbers,
      // 新方法的结果
      htmlParsingResult: pageContent,
      // 合并和对比结果
      comparison: {
        totalFromExisting: existingNumbers.length,
        totalFromHtmlParsing: pageContent.superscriptNumbers.length,
        isConsistent: existingNumbers.length === pageContent.superscriptNumbers.length,
        missingInExisting: pageContent.superscriptNumbers.filter(
          (num) => !existingNumbers.includes(num)
        ),
        missingInHtmlParsing: existingNumbers.filter(
          (num) => !pageContent.superscriptNumbers.includes(num)
        )
      }
    }
  } catch (error) {
    console.error('获取综合上标数据时发生错误:', error)
    return {
      existingAnnotationNumbers: [],
      htmlParsingResult: {
        fullHtml: '',
        superscripts: [],
        superscriptValues: [],
        superscriptNumbers: [],
        timestamp: Date.now()
      },
      comparison: {
        totalFromExisting: 0,
        totalFromHtmlParsing: 0,
        isConsistent: false,
        missingInExisting: [],
        missingInHtmlParsing: []
      }
    }
  }
}
