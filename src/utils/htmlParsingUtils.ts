/**
 * HTML解析和上标提取工具函数
 * 用于解析HTML内容并提取上标元素及其相关信息
 */

import { getAllAnnotationNumbers } from './annotationUtils'

/**
 * 上标数据接口定义
 */
export interface SuperscriptData {
  /** 上标的数值内容 */
  value: string
  /** 上标的数字编号（如果是数字格式） */
  number?: number
  /** 上标在HTML中的位置索引 */
  position: number
  /** 上标的原始HTML内容 */
  rawHtml: string
  /** 上标周围的上下文文本 */
  context?: string
}

/**
 * 页面内容解析结果接口
 */
export interface PageContentData {
  /** 完整的HTML内容 */
  fullHtml: string
  /** 提取的所有上标数据 */
  superscripts: SuperscriptData[]
  /** 上标数值数组（简化格式） */
  superscriptValues: string[]
  /** 上标数字编号数组（仅数字格式的上标） */
  superscriptNumbers: number[]
  /** 解析时间戳 */
  timestamp: number
}

/**
 * 从HTML内容中提取所有上标元素
 * @param htmlContent HTML内容字符串
 * @param includeContext 是否包含上下文信息，默认为true
 * @param contextLength 上下文长度，默认为50个字符
 * @returns 上标数据数组
 */
export const extractSuperscriptsFromHTML = (
  htmlContent: string,
  includeContext: boolean = true,
  contextLength: number = 50
): SuperscriptData[] => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return []
  }

  try {
    const superscripts: SuperscriptData[] = []

    // 使用更全面的正则表达式来匹配各种上标格式
    const comprehensiveSupRegex = /<sup[^>]*>(.*?)<\/sup>/gi
    let match

    while ((match = comprehensiveSupRegex.exec(htmlContent)) !== null) {
      const fullMatch = match[0] // 完整的上标HTML
      const content = match[1] // 上标内容
      const position = match.index // 在HTML中的位置

      // 提取数字编号（如果存在）
      let number: number | undefined
      const numberMatch = content.match(/\[?(\d+)\]?/)
      if (numberMatch) {
        number = parseInt(numberMatch[1], 10)
      }

      // 提取上下文信息
      let context: string | undefined
      if (includeContext) {
        const startPos = Math.max(0, position - contextLength)
        const endPos = Math.min(htmlContent.length, position + fullMatch.length + contextLength)
        context = htmlContent
          .substring(startPos, endPos)
          .replace(/<[^>]*>/g, '') // 移除HTML标签
          .trim()
      }

      superscripts.push({
        value: content.trim(),
        number,
        position,
        rawHtml: fullMatch,
        context
      })
    }

    // 按位置排序
    return superscripts.sort((a, b) => a.position - b.position)
  } catch (error) {
    console.error('提取上标时发生错误:', error)
    return []
  }
}

/**
 * 解析页面内容，提取HTML和上标信息
 * @param editor TipTap编辑器实例
 * @param includeContext 是否包含上下文信息
 * @returns 页面内容解析结果
 */
export const parsePageContent = (editor: any, includeContext: boolean = true): PageContentData => {
  if (!editor || !editor.getHTML) {
    return {
      fullHtml: '',
      superscripts: [],
      superscriptValues: [],
      superscriptNumbers: [],
      timestamp: Date.now()
    }
  }

  try {
    // 获取完整的HTML内容
    const fullHtml = editor.getHTML()

    // 提取上标数据
    const superscripts = extractSuperscriptsFromHTML(fullHtml, includeContext)

    // 生成简化的数据格式
    const superscriptValues = superscripts.map((sup) => sup.value)
    const superscriptNumbers = superscripts
      .filter((sup) => sup.number !== undefined)
      .map((sup) => sup.number!)
      .filter((num, index, arr) => arr.indexOf(num) === index) // 去重
      .sort((a, b) => a - b)

    return {
      fullHtml,
      superscripts,
      superscriptValues,
      superscriptNumbers,
      timestamp: Date.now()
    }
  } catch (error) {
    console.error('解析页面内容时发生错误:', error)
    return {
      fullHtml: '',
      superscripts: [],
      superscriptValues: [],
      superscriptNumbers: [],
      timestamp: Date.now()
    }
  }
}

/**
 * 格式化上标数据为不同的输出格式
 * @param superscripts 上标数据数组
 * @param format 输出格式：'array' | 'object' | 'detailed'
 * @returns 格式化后的数据
 */
export const formatSuperscriptData = (
  superscripts: SuperscriptData[],
  format: 'array' | 'object' | 'detailed' = 'array'
): any => {
  if (!superscripts || superscripts.length === 0) {
    return format === 'array' ? [] : {}
  }

  switch (format) {
    case 'array':
      // 返回简单的值数组
      return superscripts.map((sup) => sup.value)

    case 'object':
      // 返回以位置为键的对象
      return superscripts.reduce(
        (acc, sup, index) => {
          acc[index] = {
            value: sup.value,
            number: sup.number,
            position: sup.position
          }
          return acc
        },
        {} as Record<number, any>
      )

    case 'detailed':
      // 返回完整的详细信息
      return {
        total: superscripts.length,
        values: superscripts.map((sup) => sup.value),
        numbers: superscripts.filter((sup) => sup.number).map((sup) => sup.number),
        positions: superscripts.map((sup) => sup.position),
        details: superscripts
      }

    default:
      return superscripts
  }
}

/**
 * 验证上标数据的完整性
 * @param superscripts 上标数据数组
 * @returns 验证结果
 */
export const validateSuperscriptData = (
  superscripts: SuperscriptData[]
): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} => {
  const errors: string[] = []
  const warnings: string[] = []

  if (!Array.isArray(superscripts)) {
    errors.push('上标数据必须是数组格式')
    return { isValid: false, errors, warnings }
  }

  // 检查重复的数字编号
  const numbers = superscripts.filter((sup) => sup.number !== undefined).map((sup) => sup.number!)

  const duplicateNumbers = numbers.filter((num, index) => numbers.indexOf(num) !== index)
  if (duplicateNumbers.length > 0) {
    warnings.push(`发现重复的上标编号: ${[...new Set(duplicateNumbers)].join(', ')}`)
  }

  // 检查空值
  const emptyValues = superscripts.filter((sup) => !sup.value || sup.value.trim() === '')
  if (emptyValues.length > 0) {
    warnings.push(`发现${emptyValues.length}个空的上标值`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 与现有annotationUtils集成的便捷函数
 * @param editor TipTap编辑器实例
 * @returns 结合两种方法的上标数据
 */
export const getComprehensiveSuperscriptData = (editor: any) => {
  try {
    // 使用现有的annotationUtils函数
    const existingNumbers = getAllAnnotationNumbers(editor)

    // 使用新的HTML解析方法
    const pageContent = parsePageContent(editor)

    return {
      // 现有方法的结果
      existingAnnotationNumbers: existingNumbers,
      // 新方法的结果
      htmlParsingResult: pageContent,
      // 合并和对比结果
      comparison: {
        totalFromExisting: existingNumbers.length,
        totalFromHtmlParsing: pageContent.superscriptNumbers.length,
        isConsistent: existingNumbers.length === pageContent.superscriptNumbers.length,
        missingInExisting: pageContent.superscriptNumbers.filter(
          (num) => !existingNumbers.includes(num)
        ),
        missingInHtmlParsing: existingNumbers.filter(
          (num) => !pageContent.superscriptNumbers.includes(num)
        )
      }
    }
  } catch (error) {
    console.error('获取综合上标数据时发生错误:', error)
    return {
      existingAnnotationNumbers: [],
      htmlParsingResult: {
        fullHtml: '',
        superscripts: [],
        superscriptValues: [],
        superscriptNumbers: [],
        timestamp: Date.now()
      },
      comparison: {
        totalFromExisting: 0,
        totalFromHtmlParsing: 0,
        isConsistent: false,
        missingInExisting: [],
        missingInHtmlParsing: []
      }
    }
  }
}
