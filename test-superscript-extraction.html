<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上标提取功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>上标提取功能测试页面</h1>
    
    <div class="test-section">
        <h2>测试HTML内容</h2>
        <div id="test-content">
            <p>这是一个包含上标的段落<sup>[1]</sup>，用于测试上标提取功能。</p>
            <p>另一个段落包含多个上标<sup>[2]</sup>和<sup>[3]</sup>。</p>
            <p>还有一些不同格式的上标：<sup>4</sup>和<sup>注释</sup>。</p>
            <h2>参考文献</h2>
            <p>[1] 第一个参考文献的内容</p>
            <p>[2] 第二个参考文献的内容</p>
            <p>[3] 第三个参考文献的内容</p>
        </div>
    </div>

    <div class="test-section">
        <h2>测试控制</h2>
        <button onclick="testExtractSuperscripts()">测试上标提取</button>
        <button onclick="testParsePageContent()">测试页面内容解析</button>
        <button onclick="testFormatData()">测试数据格式化</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div class="test-section">
        <h2>测试结果</h2>
        <div id="results"></div>
    </div>

    <script type="module">
        // 模拟我们的HTML解析工具函数
        function extractSuperscriptsFromHTML(htmlContent, includeContext = true, contextLength = 50) {
            if (!htmlContent || typeof htmlContent !== 'string') {
                return [];
            }

            try {
                const superscripts = [];
                const comprehensiveSupRegex = /<sup[^>]*>(.*?)<\/sup>/gi;
                let match;

                while ((match = comprehensiveSupRegex.exec(htmlContent)) !== null) {
                    const fullMatch = match[0];
                    const content = match[1];
                    const position = match.index;

                    let number;
                    const numberMatch = content.match(/\[?(\d+)\]?/);
                    if (numberMatch) {
                        number = parseInt(numberMatch[1], 10);
                    }

                    let context;
                    if (includeContext) {
                        const startPos = Math.max(0, position - contextLength);
                        const endPos = Math.min(htmlContent.length, position + fullMatch.length + contextLength);
                        context = htmlContent.substring(startPos, endPos)
                            .replace(/<[^>]*>/g, '')
                            .trim();
                    }

                    superscripts.push({
                        value: content.trim(),
                        number,
                        position,
                        rawHtml: fullMatch,
                        context
                    });
                }

                return superscripts.sort((a, b) => a.position - b.position);
            } catch (error) {
                console.error('提取上标时发生错误:', error);
                return [];
            }
        }

        function parsePageContent(htmlContent, includeContext = true) {
            try {
                const superscripts = extractSuperscriptsFromHTML(htmlContent, includeContext);
                
                const superscriptValues = superscripts.map(sup => sup.value);
                const superscriptNumbers = superscripts
                    .filter(sup => sup.number !== undefined)
                    .map(sup => sup.number)
                    .filter((num, index, arr) => arr.indexOf(num) === index)
                    .sort((a, b) => a - b);

                return {
                    fullHtml: htmlContent,
                    superscripts,
                    superscriptValues,
                    superscriptNumbers,
                    timestamp: Date.now()
                };
            } catch (error) {
                console.error('解析页面内容时发生错误:', error);
                return {
                    fullHtml: '',
                    superscripts: [],
                    superscriptValues: [],
                    superscriptNumbers: [],
                    timestamp: Date.now()
                };
            }
        }

        function formatSuperscriptData(superscripts, format = 'array') {
            if (!superscripts || superscripts.length === 0) {
                return format === 'array' ? [] : {};
            }

            switch (format) {
                case 'array':
                    return superscripts.map(sup => sup.value);
                case 'object':
                    return superscripts.reduce((acc, sup, index) => {
                        acc[index] = {
                            value: sup.value,
                            number: sup.number,
                            position: sup.position
                        };
                        return acc;
                    }, {});
                case 'detailed':
                    return {
                        total: superscripts.length,
                        values: superscripts.map(sup => sup.value),
                        numbers: superscripts.filter(sup => sup.number).map(sup => sup.number),
                        positions: superscripts.map(sup => sup.position),
                        details: superscripts
                    };
                default:
                    return superscripts;
            }
        }

        // 测试函数
        window.testExtractSuperscripts = function() {
            const testContent = document.getElementById('test-content');
            const htmlContent = testContent.innerHTML;
            const result = extractSuperscriptsFromHTML(htmlContent);
            
            displayResult('上标提取结果', result);
        };

        window.testParsePageContent = function() {
            const testContent = document.getElementById('test-content');
            const htmlContent = testContent.innerHTML;
            const result = parsePageContent(htmlContent);
            
            displayResult('页面内容解析结果', result);
        };

        window.testFormatData = function() {
            const testContent = document.getElementById('test-content');
            const htmlContent = testContent.innerHTML;
            const superscripts = extractSuperscriptsFromHTML(htmlContent);
            
            const results = {
                array: formatSuperscriptData(superscripts, 'array'),
                object: formatSuperscriptData(superscripts, 'object'),
                detailed: formatSuperscriptData(superscripts, 'detailed')
            };
            
            displayResult('数据格式化结果', results);
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function displayResult(title, data) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `<strong>${title}:</strong>\n${JSON.stringify(data, null, 2)}`;
            resultsDiv.appendChild(resultDiv);
        }

        // 页面加载完成后自动运行一次测试
        window.addEventListener('load', function() {
            console.log('页面加载完成，可以开始测试上标提取功能');
        });
    </script>
</body>
</html>
