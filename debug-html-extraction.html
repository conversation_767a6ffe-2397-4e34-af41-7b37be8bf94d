<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML提取调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .selectable {
            border: 1px dashed #ccc;
            padding: 10px;
            margin: 10px 0;
            user-select: text;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>HTML提取调试页面</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>这个页面用于调试 <code>getSelectedHTMLContent</code> 函数的问题。</p>
        <p>请在下面的文本中选中包含上标的内容，然后查看控制台输出。</p>
    </div>

    <div class="test-section">
        <h2>测试内容</h2>
        <div class="selectable" id="test-content">
            <p>这是一个包含上标的段落<sup>[1]</sup>，用于测试HTML提取功能。</p>
            <p>另一个段落包含多个上标<sup>[2]</sup>和<sup>[3]</sup>。</p>
            <p>还有一些不同格式的上标：<sup>4</sup>和<sup>注释</sup>。</p>
            <p>普通文本没有上标，用于测试选择范围的准确性。</p>
        </div>
    </div>

    <div class="test-section">
        <h2>调试信息</h2>
        <p>请打开浏览器的开发者工具查看控制台输出。</p>
        <button onclick="testSelection()">测试当前选择</button>
        <button onclick="clearResults()">清除结果</button>
        <div id="results" class="result"></div>
    </div>

    <script>
        // 模拟 TipTap 编辑器的基本结构用于测试
        const mockEditor = {
            state: {
                doc: {
                    textBetween: function(from, to) {
                        const selection = window.getSelection();
                        return selection ? selection.toString() : '';
                    },
                    slice: function(from, to) {
                        return {
                            content: {
                                toJSON: function() {
                                    return {
                                        content: [
                                            {
                                                type: 'paragraph',
                                                content: [
                                                    {
                                                        type: 'text',
                                                        text: '测试文本',
                                                        marks: [{ type: 'superscript' }]
                                                    }
                                                ]
                                            }
                                        ]
                                    };
                                }
                            },
                            size: 10
                        };
                    }
                },
                schema: {
                    serializer: null // 模拟没有serializer的情况
                }
            },
            getHTML: function() {
                return document.getElementById('test-content').innerHTML;
            }
        };

        function testSelection() {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) {
                displayResult('请先选中一些文本');
                return;
            }

            const range = selection.getRangeAt(0);
            const selectedText = selection.toString();
            
            // 模拟调用 getSelectedHTMLContent
            console.log('=== 开始测试 HTML 提取 ===');
            console.log('选中文本:', selectedText);
            
            // 获取选中内容的HTML
            const selectedHTML = range.cloneContents();
            const tempDiv = document.createElement('div');
            tempDiv.appendChild(selectedHTML);
            const htmlContent = tempDiv.innerHTML;
            
            console.log('提取的HTML:', htmlContent);
            
            const result = {
                selectedText: selectedText,
                selectedHTML: htmlContent,
                hasSupTags: htmlContent.includes('<sup>'),
                supCount: (htmlContent.match(/<sup>/g) || []).length
            };
            
            displayResult(JSON.stringify(result, null, 2));
        }

        function displayResult(content) {
            document.getElementById('results').textContent = content;
        }

        function clearResults() {
            document.getElementById('results').textContent = '';
            console.clear();
        }

        // 监听选择变化
        document.addEventListener('selectionchange', function() {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const selectedText = selection.toString();
                if (selectedText.trim()) {
                    console.log('选择变化:', selectedText);
                }
            }
        });
    </script>
</body>
</html>
